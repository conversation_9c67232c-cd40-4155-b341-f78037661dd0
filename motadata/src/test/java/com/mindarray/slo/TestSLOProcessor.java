package com.mindarray.slo;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.SLOCycle;
import com.mindarray.api.SLOProfile;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(180 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSLOProcessor
{
    private static final Logger LOGGER = new Logger(TestSLOProcessor.class, GlobalConstants.MOTADATA_SLO, "Test SLO Processor");

    // Test data context
    private static final JsonObject context = new JsonObject();
    private static MessageConsumer<JsonObject> messageConsumer;
    private static final long objectId = 12345L;
    private static final String instance = "test-instance";

    // SLO Processor instance and deployment
    private static SLOProcessor sloProcessor;
    private static String deploymentId;

    // Reflection fields
    private static Field sloDetailsField;
    private static Field sloCyclesField;
    private static Field availabilitySLOProfilesField;
    private static Field sloEventsField;
    private static Field countField;

    // Calculation methods via reflection
    private static Method getSLOAchievedPercentageMethod;
    private static Method getSLOViolatedSecondsMethod;
    private static Method getSLOMeanTimeToResolveMethod;
    private static Method getSLOMeanTimeBetweenFailureMethod;
    private static Method getSLOErrorBudgetLeftPercentageMethod;
    private static Method getSLOErrorBudgetLeftSecondsMethod;
    private static Method getSLOBurnRateSecondsMethod;
    private static Method getSLOBurnRateMethod;
    private static Method getSLOStatusMethod;
    private static Method updateSLOMethod;

    // Cycle management methods via reflection
    private static Method qualifyCyclesMethod;
    private static Method startCycleMethod;
    private static Method qualifyAndAssignEntitiesMethod;
    private static Method filterMethod;
    private static Method processMethod;



    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            // Get existing SLO profiles created by TestSLOProfile
            var monitorAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorAvailabilitySLO");
            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorPerformanceSLO");
            var instanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstanceAvailabilitySLO");

            // Store profile IDs for testing
            context.put("monitorAvailabilityId", monitorAvailabilitySLO.getLong(ID));
            context.put("monitorPerformanceId", monitorPerformanceSLO.getLong(ID));
            context.put("instanceAvailabilityId", instanceAvailabilitySLO.getLong(ID));

            // Create and deploy SLOProcessor verticle
            sloProcessor = new SLOProcessor();

            TestUtil.vertx().undeploy(Bootstrap.getDeployedVerticles().remove(SLOProcessor.class.getSimpleName()), undeployResult ->
            {
                if (undeployResult.succeeded())
                {
                    LOGGER.info("SLO Processor verticle undeployed successfully");

                    TestUtil.vertx().deployVerticle(sloProcessor, deployResult ->
                    {
                        if (deployResult.succeeded())
                        {
                            deploymentId = deployResult.result();

                            try
                            {
                                Bootstrap.getDeployedVerticles().put(SLOProcessor.class.getSimpleName(), deploymentId);

                                // Setup reflection access to private fields and methods after deployment
                                setupReflectionFields();
                                setupReflectionMethods();

                                LOGGER.info("SLO Processor verticle deployed successfully with ID: " + deploymentId);

                                testContext.completeNow();
                            }
                            catch (Exception exception)
                            {
                                LOGGER.warn("Failed to setup reflection");

                                LOGGER.error(exception);

                                testContext.failNow(exception.getMessage());
                            }
                        }
                        else
                        {
                            LOGGER.warn("Failed to deploy SLO Processor verticle");

                            LOGGER.error(deployResult.cause());

                            testContext.failNow(deployResult.cause().getMessage());
                        }
                    });
                }
                else
                {
                    LOGGER.warn("Failed to undeploy SLO Processor verticle");

                    LOGGER.error(undeployResult.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("Running test case: %s", testInfo.getTestMethod().get().getName()));
        testContext.completeNow();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    // ======================== Setup Helper Methods ========================

    /**
     * Setup reflection access to private fields of SLOProcessor
     */
    private static void setupReflectionFields() throws Exception
    {
        sloDetailsField = SLOProcessor.class.getDeclaredField("sloDetails");
        sloDetailsField.setAccessible(true);

        sloCyclesField = SLOProcessor.class.getDeclaredField("sloCycles");
        sloCyclesField.setAccessible(true);

        availabilitySLOProfilesField = SLOProcessor.class.getDeclaredField("availabilitySLOProfiles");
        availabilitySLOProfilesField.setAccessible(true);

        sloEventsField = SLOProcessor.class.getDeclaredField("sloEvents");
        sloEventsField.setAccessible(true);

        countField = SLOProcessor.class.getDeclaredField("count");
        countField.setAccessible(true);
    }

    /**
     * Setup reflection access to private methods of SLOProcessor
     */
    private static void setupReflectionMethods() throws Exception
    {
        getSLOAchievedPercentageMethod = SLOProcessor.class.getDeclaredMethod("getSLOAchievedPercentage", JsonObject.class, JsonObject.class);
        getSLOAchievedPercentageMethod.setAccessible(true);

        getSLOViolatedSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOViolatedSeconds", JsonObject.class, JsonObject.class);
        getSLOViolatedSecondsMethod.setAccessible(true);

        getSLOMeanTimeToResolveMethod = SLOProcessor.class.getDeclaredMethod("getSLOMeanTimeToResolve", JsonObject.class, JsonObject.class);
        getSLOMeanTimeToResolveMethod.setAccessible(true);

        getSLOMeanTimeBetweenFailureMethod = SLOProcessor.class.getDeclaredMethod("getSLOMeanTimeBetweenFailure", JsonObject.class, JsonObject.class, long.class);
        getSLOMeanTimeBetweenFailureMethod.setAccessible(true);

        getSLOErrorBudgetLeftPercentageMethod = SLOProcessor.class.getDeclaredMethod("getSLOErrorBudgetLeftPercentage", JsonObject.class, JsonObject.class);
        getSLOErrorBudgetLeftPercentageMethod.setAccessible(true);

        getSLOErrorBudgetLeftSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOErrorBudgetLeftSeconds", JsonObject.class, JsonObject.class);
        getSLOErrorBudgetLeftSecondsMethod.setAccessible(true);

        getSLOBurnRateSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOBurnRateSeconds", JsonObject.class, JsonObject.class);
        getSLOBurnRateSecondsMethod.setAccessible(true);

        getSLOBurnRateMethod = SLOProcessor.class.getDeclaredMethod("getSLOBurnRate", long.class, JsonObject.class);
        getSLOBurnRateMethod.setAccessible(true);

        getSLOStatusMethod = SLOProcessor.class.getDeclaredMethod("getSLOStatus", JsonObject.class, JsonObject.class, int.class, int.class);
        getSLOStatusMethod.setAccessible(true);

        updateSLOMethod = SLOProcessor.class.getDeclaredMethod("updateSLO", JsonObject.class, SLOConstants.SLOFlapStatus.class, long.class, long.class, String.class, long.class, long.class, boolean.class);
        updateSLOMethod.setAccessible(true);

        // Cycle management methods
        qualifyCyclesMethod = SLOProcessor.class.getDeclaredMethod("qualifyCycles", JsonArray.class, AtomicInteger.class);
        qualifyCyclesMethod.setAccessible(true);

        startCycleMethod = SLOProcessor.class.getDeclaredMethod("startCycle", JsonObject.class, long.class, long.class);
        startCycleMethod.setAccessible(true);

        qualifyAndAssignEntitiesMethod = SLOProcessor.class.getDeclaredMethod("qualifyAndAssignEntities", JsonObject.class, JsonArray.class, long.class, long.class);
        qualifyAndAssignEntitiesMethod.setAccessible(true);

        filterMethod = SLOProcessor.class.getDeclaredMethod("filter", JsonObject.class, JsonObject.class);
        filterMethod.setAccessible(true);

        processMethod = SLOProcessor.class.getDeclaredMethod("process", JsonObject.class);
        processMethod.setAccessible(true);
    }

    // ======================== SLO Cycle Management Tests ========================

    /**
     * Test SLO cycle qualification with expired cycles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testSLOCycleQualificationStartNewCycle(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            var monitorPerformanceId =  context.getLong("monitorPerformanceId");

            var sloProfiles = new JsonArray()
                    .add(SLOProfileConfigStore.getStore().getItem(monitorAvailabilityId))
                    .add(SLOProfileConfigStore.getStore().getItem(monitorPerformanceId));

            var index = new AtomicInteger(0);

            // Get initial cycles count
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);
            var initialCyclesCount = sloCyclesMap.size();

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);
            var initialDetailsSize = sloDetailsMap.size();

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(1), v ->
            {
                try
                {
                    // Test cycle qualification
                    qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, index);

                    var count = new AtomicInteger(0);

                    TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
                    {
                        if (sloCyclesMap.size() > initialCyclesCount)
                        {
                            assertNotNull(sloCyclesMap.get(monitorAvailabilityId));

                            assertNotNull(sloCyclesMap.get(monitorPerformanceId));

                            var entityMap = sloDetailsMap.get(monitorAvailabilityId);

                            assertTrue(sloDetailsMap.size() >= initialDetailsSize, "SLO details map should grow");

                            assertNotNull(entityMap, "Entity map should be created");

                            entityMap = sloDetailsMap.get(monitorPerformanceId);

                            assertNotNull(entityMap, "Entity map should be created");

                            LOGGER.info(String.format("%s: start new cycle qualification test successful", testInfo.getTestMethod().get().getName()));

                            testContext.completeNow();

                            TestUtil.vertx().cancelTimer(timer);
                        }
                        else if (count.incrementAndGet() > 2)
                        {
                            testContext.failNow("new cycle failed to start!");

                            TestUtil.vertx().cancelTimer(timer);
                        }
                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception.getMessage());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle qualification with active cycles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testSLOCycleQualificationActiveCycles(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            var sloProfiles = new JsonArray().add(SLOProfileConfigStore.getStore().getItem(monitorAvailabilityId));

            var index = new AtomicInteger(0);

            // Get initial cycles count
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);
            var initialCyclesCount = sloCyclesMap.size();

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);
            var initialDetailsSize = sloDetailsMap.size();

            var existingCycleId = sloCyclesMap.get(monitorAvailabilityId);

            // Test cycle qualification
            qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, index);

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), timer ->
            {
                // Verify that active cycle is not restarted
                assertEquals(initialCyclesCount, sloCyclesMap.size(), "Active cycle should not be restarted");
                assertEquals(existingCycleId, sloCyclesMap.get(monitorAvailabilityId).longValue(),
                        "Existing cycle ID should remain unchanged");

                assertEquals(initialDetailsSize, sloDetailsMap.size(), "SLO details map should grow");

                LOGGER.info(String.format("%s: Active cycle qualification test successful", testInfo.getTestMethod().get().getName()));

                testContext.completeNow();
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Calculation Tests ========================

    /**
     * Test SLO achieved percentage calculation
     * Formula: (duration left * 100) / total duration
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testSLOAchievedPercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Create test data with known values
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 8640L); // 86.4% of 10000 seconds left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 10000L); // Total duration 10000 seconds

            // Expected: (8640 * 100) / 10000 = 86.4%
            var expectedPercentage = 86.4f;

            var actualPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedPercentage, actualPercentage, 0.01f, "SLO achieved percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedPercentage, actualPercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO violated seconds calculation
     * Formula: total duration - duration left
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSLOViolatedSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 7200L); // 2 hours left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Expected: 86400 - 7200 = 79200 seconds violated
            var expectedViolatedSeconds = 79200L;

            var actualViolatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedViolatedSeconds, actualViolatedSeconds, "SLO violated seconds calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedViolatedSeconds, actualViolatedSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Mean Time To Resolve (MTTR) calculation
     * Formula: violated seconds / down incident count
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testSLOMeanTimeToResolveCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 4); // 4 down incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Violated seconds: 86400 - 82800 = 3600 seconds
            // MTTR: 3600 / 4 = 900 seconds (15 minutes per incident)
            var expectedMTTR = 900L;

            var actualMTTR = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedMTTR, actualMTTR, "MTTR calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTTR, actualMTTR));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test MTTR calculation with zero incidents (edge case)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testSLOMeanTimeToResolveWithZeroIncidents(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L) // Full duration left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0); // No incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Expected: 0 (no incidents means no MTTR)
            var expectedMTTR = 0L;

            var actualMTTR = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedMTTR, actualMTTR, "MTTR calculation with zero incidents failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTTR, actualMTTR));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Mean Time Between Failure (MTBF) calculation
     * Formula: (total duration - violated seconds) / up incident count
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testSLOMeanTimeBetweenFailureCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.UP_INCIDENT_COUNT, 3); // 3 up incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            var duration = 86400L;

            // Violated seconds: 86400 - 82800 = 3600 seconds
            // Uptime: 86400 - 3600 = 82800 seconds
            // MTBF: 82800 / 3 = 27600 seconds (7.67 hours between failures)
            var expectedMTBF = 27600L;

            var actualMTBF = (Long) getSLOMeanTimeBetweenFailureMethod.invoke(sloProcessor, sloData, sloCycle, duration);

            assertEquals(expectedMTBF, actualMTBF, "MTBF calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTBF, actualMTBF));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Error Budget Left Percentage calculation
     * Formula: 100 - ((total duration - duration left) * 100 / acceptable violation time)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testSLOErrorBudgetLeftPercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 85500L); // 900 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours total
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget (2.4 hours)

            // Violated: 86400 - 85500 = 900 seconds
            // Error budget used: (900 * 100) / 8640 = 10.42%
            // Error budget left: 100 - 10.42 = 89.58%
            var expectedErrorBudgetLeft = 89.58f;

            var actualErrorBudgetLeft = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedErrorBudgetLeft, actualErrorBudgetLeft, 0.01f, "Error budget left percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedErrorBudgetLeft, actualErrorBudgetLeft));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Error Budget Left Seconds calculation
     * Formula: acceptable violation time - (total duration - duration left)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testSLOErrorBudgetLeftSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 84600L); // 1800 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours total
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            // Violated: 86400 - 84600 = 1800 seconds
            // Error budget left: 8640 - 1800 = 6840 seconds
            var expectedErrorBudgetLeftSeconds = 6840L;

            var actualErrorBudgetLeftSeconds = (Long) getSLOErrorBudgetLeftSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedErrorBudgetLeftSeconds, actualErrorBudgetLeftSeconds, "Error budget left seconds calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedErrorBudgetLeftSeconds, actualErrorBudgetLeftSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Burn Rate Seconds calculation
     * Formula: current violated seconds - last violated seconds
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testSLOBurnRateSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 84600L) // Current: 1800 seconds violated
                    .put(SLOConstants.LAST_VIOLATED_SECONDS, 900L); // Previous: 900 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Current violated: 86400 - 84600 = 1800 seconds
            // Burn rate: 1800 - 900 = 900 seconds burned since last calculation
            var expectedBurnRateSeconds = 900L;

            var actualBurnRateSeconds = (Long) getSLOBurnRateSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedBurnRateSeconds, actualBurnRateSeconds, "Burn rate seconds calculation failed");

            // Verify that last violated seconds is updated
            assertEquals(1800L, sloData.getLong(SLOConstants.LAST_VIOLATED_SECONDS).longValue(), "Last violated seconds should be updated");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedBurnRateSeconds, actualBurnRateSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Burn Rate Percentage calculation
     * Formula: (burn rate seconds * 100) / total duration
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testSLOBurnRatePercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var burnRateSeconds = 1800L; // 30 minutes burned

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Expected: (1800 * 100) / 86400 = 2.08%
            var expectedBurnRatePercentage = 2.08f;

            var actualBurnRatePercentage = (Float) getSLOBurnRateMethod.invoke(sloProcessor, burnRateSeconds, sloCycle);

            assertEquals(expectedBurnRatePercentage, actualBurnRatePercentage, 0.01f, "Burn rate percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedBurnRatePercentage, actualBurnRatePercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Status determination based on achieved percentage
     * OK: >= warning threshold
     * WARNING: >= target but < warning
     * BREACHED: < target
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testSLOStatusCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 10000L);

            var target = 90; // 90% target
            var warning = 95; // 95% warning

            // Test BREACHED status (< 90%)
            var sloDataBreached = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 8500L); // 85% achieved

            var statusBreached = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataBreached, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.BREACHED.ordinal(), statusBreached.intValue(), "BREACHED status calculation failed");

            // Test WARNING status (>= 90% but < 95%)
            var sloDataWarning = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 9200L); // 92% achieved

            var statusWarning = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataWarning, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.WARNING.ordinal(), statusWarning.intValue(), "WARNING status calculation failed");

            // Test OK status (>= 95%)
            var sloDataOk = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 9600L); // 96% achieved

            var statusOk = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataOk, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.OK.ordinal(), statusOk.intValue(), "OK status calculation failed");

            LOGGER.info(String.format("%s: BREACHED=%d, WARNING=%d, OK=%d", testInfo.getTestMethod().get().getName(), statusBreached, statusWarning, statusOk));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Update and State Management Tests ========================

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testAddDummyEntityForAvailabilitySLO(VertxTestContext testContext)
    {
        try
        {
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L); // Full duration initially

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            sloDetailsMap.get(monitorAvailabilityId).put(objectId + INSTANCE_SEPARATOR + instance, sloData);

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update for first poll (initialization)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testSLOUpdateFirstPoll(VertxTestContext testContext)
    {
        try
        {
            var currentTimestamp = DateTimeUtil.currentSeconds();

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_UP)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_SLO_STATUS) != null)
                {
                    assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
                    assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be HEALTHY");
                    assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be set");
                    assertEquals(1, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Up incident count should be 1");

                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testSLOUpdateStatusChange(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorAvailabilityTimestamp") + 10;

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_DOWN)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_BREACHED_TIMESTAMP) != null)
                {
                    assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should change to DEGRADED");
                    assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be updated");
                    assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be set");
                    assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down incident count should increment");

                    LOGGER.info(String.format("%s: Status change from HEALTHY to DEGRADED successful", testInfo.getTestMethod().get().getName()));

                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testSLOUpdateContinuousDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorAvailabilityTimestamp") + 60;

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_DOWN)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_BREACHED_TIMESTAMP) != null)
                {
                    var expectedDurationLeft = 86400 - 60;
                    assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");
                    assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be updated");

                    LOGGER.info(String.format("%s: Continuous degraded state tracking successful. Duration reduced by %d seconds",
                            testInfo.getTestMethod().get().getName(), 60));


                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testSLOUpdateDisableFromDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorAvailabilityTimestamp") + 60;

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_DISABLE)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_SLO_STATUS) == SLOConstants.SLOFlapStatus.NOT_CALCULATED.ordinal())
                {
                    var expectedDurationLeft = 86400 - 120;
                    assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");

                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testSLOUpdateRecoveryFromDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorAvailabilityTimestamp") + 60;

            context.put("monitorAvailabilityTimestamp", currentTimestamp);

            var event = new JsonObject()
                    .put(SEVERITY, STATUS_UP)
                    .put(AIOpsConstants.ENTITY_ID, objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorAvailabilityId = context.getLong("monitorAvailabilityId");

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, event);

            var count = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                var sloData = sloDetailsMap.get(monitorAvailabilityId).get(objectId + INSTANCE_SEPARATOR + instance);

                if (sloData.getInteger(SLOConstants.LAST_SLO_STATUS) == SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                {
                    var expectedDurationLeft = 86400 - 120;
                    assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");

                    testContext.completeNow();

                    TestUtil.vertx().cancelTimer(timer);
                }
                else if (count.incrementAndGet() > 2)
                {
                    testContext.failNow("new cycle failed to start!");

                    TestUtil.vertx().cancelTimer(timer);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testGlobalSLOUpdateFirstPoll(VertxTestContext testContext)
    {
        try
        {
            var currentTimestamp = DateTimeUtil.currentSeconds();

            context.put("monitorPerformanceTimestamp", currentTimestamp);

            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorPerformanceId = context.getLong("monitorPerformanceId");

            var sloData = sloDetailsMap.get(monitorPerformanceId).get(EMPTY_VALUE);

            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, currentTimestamp, NOT_AVAILABLE, EMPTY_VALUE, monitorPerformanceId, sloCyclesMap.get(monitorPerformanceId), false);

            assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
            assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be DEGRADED");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be set");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be set");
            assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down incident count should be 1");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testGlobalSLOUpdateContinuousDegraded(VertxTestContext testContext)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorPerformanceTimestamp") + 60;

            context.put("monitorPerformanceTimestamp", currentTimestamp);

            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorPerformanceId = context.getLong("monitorPerformanceId");

            var sloData = sloDetailsMap.get(monitorPerformanceId).get(EMPTY_VALUE);

            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, currentTimestamp, NOT_AVAILABLE, EMPTY_VALUE, monitorPerformanceId, sloCyclesMap.get(monitorPerformanceId), false);

            assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
            assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be DEGRADED");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be set");
            assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down incident count should be 1");
            var expectedDurationLeft = TimeUnit.DAYS.toSeconds(30) - 60;
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testGlobalSLOUpdateRecoveryFromDegradedState(VertxTestContext testContext)
    {
        try
        {
            var currentTimestamp = context.getLong("monitorPerformanceTimestamp") + 60;

            context.put("monitorPerformanceTimestamp", currentTimestamp);

            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(sloProcessor);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(sloProcessor);

            var monitorPerformanceId = context.getLong("monitorPerformanceId");

            var sloData = sloDetailsMap.get(monitorPerformanceId).get(EMPTY_VALUE);

            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, currentTimestamp, NOT_AVAILABLE, EMPTY_VALUE, monitorPerformanceId, sloCyclesMap.get(monitorPerformanceId), false);

            assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be HEALTHY");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be set");
            var expectedDurationLeft = TimeUnit.DAYS.toSeconds(30) - 120;
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        try
        {
            LOGGER.info("all test cases executed");

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }
}
